# 🚀 Hướng Dẫn CI/CD và Deployment

## 📋 Tổng Quan

Quy trình này bao gồm:
- ✅ Test và lint code trước khi push
- 🐳 Build Docker image
- ☁️ Deploy tự động lên Google Cloud Run
- 🔄 Migration database tự động

---

## 🔧 Setup Môi Trường Local

### 1. Clone và Cài Đặt

```bash
git clone <repository-url>
cd swinx
cp .env.example .env
composer install
npm install
php artisan key:generate
```

### 2. Cấu Hình Database Local

```bash
# Tạo database
php artisan migrate
php artisan db:seed
```

---

## 🧪 Quy Trình Test Local (TRƯỚC KHI PUSH)

### Chạy Tất Cả Test Cùng Lúc

```bash
# Chạy script tự động (khuyến nghị)
./scripts/pre-push.sh
```

### Hoặc Chạy Từng Bước

```bash
# 1. Build frontend
npm run build

# 2. Ki<PERSON>m tra code style PHP
vendor/bin/pint --test

# 3. <PERSON><PERSON>m tra frontend linting
npm run lint
npm run format:check

# 4. Kiểm tra TypeScript
npx vue-tsc --noEmit

# 5. Chạy PHP tests
./vendor/bin/pest

# 6. Generate Ziggy routes
php artisan ziggy:generate
```

### ⚠️ Quan Trọng: Chỉ Push Khi Tất Cả Test Pass

```bash
# Nếu tất cả test pass
git add .
git commit -m "feat: your changes"
git push origin main
```

---

## 🐳 Test Docker Local

### Build Image cho Production

```bash
# Build Docker image manual
docker build -t swinx-app:local .

# Test chạy container
docker run -d -p 8080:8080 \
  -e APP_ENV=local \
  -e APP_DEBUG=true \
  -e APP_KEY=base64:$(openssl rand -base64 32) \
  swinx-app:local

# Kiểm tra health
curl http://localhost:8080/health
```

---

## ☁️ Setup Google Cloud

### 1. Tạo Project và Enable Services

```bash
# Tạo project mới
gcloud projects create your-project-id

# Set project
gcloud config set project your-project-id

# Enable services
gcloud services enable \
  cloudbuild.googleapis.com \
  run.googleapis.com \
  artifactregistry.googleapis.com
```

### 2. Tạo Artifact Registry

```bash
# Tạo repository cho Docker images
gcloud artifacts repositories create swinx \
  --repository-format=docker \
  --location=asia-southeast1 \
  --description="Swinx application images"
```

### 3. Tạo Service Account

```bash
# Tạo service account
gcloud iam service-accounts create github-deploy \
  --description="Service account for GitHub Actions" \
  --display-name="GitHub Deploy"

# Cấp quyền
gcloud projects add-iam-policy-binding your-project-id \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/run.admin"

gcloud projects add-iam-policy-binding your-project-id \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/artifactregistry.writer"

gcloud projects add-iam-policy-binding your-project-id \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/iam.serviceAccountUser"

# Tạo key
gcloud iam service-accounts keys create github-deploy-key.json \
  --iam-account=<EMAIL>
```

### 4. Setup Database (Cloud SQL hoặc External)

#### Option A: Cloud SQL (Khuyến nghị cho production)

```bash
# Tạo Cloud SQL instance
gcloud sql instances create swinx-db \
  --database-version=MYSQL_8_0 \
  --tier=db-f1-micro \
  --region=asia-southeast1

# Tạo database
gcloud sql databases create swinburne --instance=swinx-db

# Tạo user
gcloud sql users create swinx-user \
  --instance=swinx-db \
  --password=your-secure-password
```

#### Option B: External MySQL (Hiện tại)

Giữ MySQL container hiện tại hoặc sử dụng external MySQL server.

---

## 🔐 Setup GitHub Secrets

Vào GitHub Repository → Settings → Secrets and Variables → Actions

### Required Secrets:

```
GCP_PROJECT_ID=your-project-id
GCP_SA_KEY=<nội dung file github-deploy-key.json>
```

### Tạo Google Cloud Secrets (cho production env):

```bash
# Tạo secrets trong Google Cloud
echo -n "base64:$(openssl rand -base64 32)" | gcloud secrets create app-key --data-file=-
echo -n "your-db-host" | gcloud secrets create db-host --data-file=-
echo -n "swinburne" | gcloud secrets create db-database --data-file=-
echo -n "swinx-user" | gcloud secrets create db-username --data-file=-
echo -n "your-secure-password" | gcloud secrets create db-password --data-file=-

# Cấp quyền cho Cloud Run service account
gcloud secrets add-iam-policy-binding app-key \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

# Làm tương tự cho các secrets khác
```

---

## 🚀 Quy Trình Deploy

### 1. Automatic Deploy (Khuyến nghị)

```bash
# Chỉ cần push lên main branch
git push origin main

# GitHub Actions sẽ tự động:
# 1. Run all tests
# 2. Build Docker image
# 3. Push to Artifact Registry
# 4. Deploy to Cloud Run
# 5. Run migrations
```

### 2. Manual Deploy

```bash
# Build và push image
docker build -t asia-southeast1-docker.pkg.dev/your-project-id/swinx/swinx-app:latest .
docker push asia-southeast1-docker.pkg.dev/your-project-id/swinx/swinx-app:latest

# Deploy to Cloud Run
gcloud run deploy swinx-app \
  --image asia-southeast1-docker.pkg.dev/your-project-id/swinx/swinx-app:latest \
  --platform managed \
  --region asia-southeast1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 1Gi \
  --cpu 1 \
  --min-instances 0 \
  --max-instances 10 \
  --set-env-vars APP_ENV=production \
  --set-env-vars APP_DEBUG=false \
  --set-env-vars LOG_CHANNEL=stderr \
  --set-secrets APP_KEY=app-key:latest \
  --set-secrets DB_HOST=db-host:latest \
  --set-secrets DB_DATABASE=db-database:latest \
  --set-secrets DB_USERNAME=db-username:latest \
  --set-secrets DB_PASSWORD=db-password:latest
```

---

## 📊 Monitoring và Debugging

### Check Application Status

```bash
# Xem logs Cloud Run
gcloud run services logs read swinx-app --region=asia-southeast1

# Xem service info
gcloud run services describe swinx-app --region=asia-southeast1
```

### Health Check

```bash
# Check health endpoint
curl https://your-app-url.run.app/health
```

### Local Debug với Docker

```bash
# Chạy container với logs
docker run -it --rm -p 8080:8080 \
  -e APP_ENV=local \
  -e APP_DEBUG=true \
  -e APP_KEY=base64:$(openssl rand -base64 32) \
  swinx-app:local

# Xem logs
docker logs -f <container-id>
```

---

## 🔄 Quy Trình Development

### 1. Feature Development

```bash
# Tạo feature branch
git checkout -b feature/new-feature

# Develop & test
./scripts/pre-push.sh  # Test trước khi commit

# Commit & push
git add .
git commit -m "feat: implement new feature"
git push origin feature/new-feature

# Tạo Pull Request
```

### 2. Code Review & Merge

- GitHub Actions sẽ tự động run tests cho PR
- Sau khi review và approve → Merge vào main
- Auto deploy sẽ chạy

### 3. Hotfix

```bash
# Hotfix trực tiếp trên main (emergency only)
git checkout main
git pull origin main

# Fix issue
./scripts/pre-push.sh  # Ensure tests pass

git add .
git commit -m "fix: urgent hotfix"
git push origin main  # Auto deploy
```

---

## 📝 Checklist Trước Khi Deploy

### ✅ Local Testing

- [ ] `./scripts/pre-push.sh` pass
- [ ] Docker Compose development test pass
- [ ] Database migrations work
- [ ] No debug statements (dd, console.log)
- [ ] Environment variables updated

### ✅ Production Setup

- [ ] Google Cloud Project configured
- [ ] Service Account có đủ quyền
- [ ] Secrets được tạo đúng
- [ ] Database accessible từ Cloud Run
- [ ] Domain name configured (nếu có)

### ✅ Post-Deploy

- [ ] Health check pass
- [ ] Application accessible
- [ ] Database connected
- [ ] Logs không có errors
- [ ] Performance acceptable

---

## 🆘 Troubleshooting

### Build Fails

```bash
# Check Dockerfile syntax
docker build -t test .

# Check dependencies
npm ci
composer install
```

### Deploy Fails

```bash
# Check GitHub Actions logs
# Check Service Account permissions
# Verify secrets exist

# Manual deploy để debug
gcloud run deploy swinx-app --source .
```

### Application Errors

```bash
# Check Cloud Run logs
gcloud run services logs read swinx-app --region=asia-southeast1

# Check container locally
docker run -it swinx-app:local sh
```

### Database Issues

```bash
# Test connection từ Cloud Run
gcloud run services update swinx-app \
  --region=asia-southeast1 \
  --add-cloudsql-instances=your-project:asia-southeast1:swinx-db

# Check database từ local
mysql -h your-db-host -u swinx-user -p swinburne
```

---

## 🔧 Advanced Configuration

### Custom Domain

```bash
# Map custom domain
gcloud run domain-mappings create --service swinx-app --domain your-domain.com --region asia-southeast1
```

### SSL Certificate (Let's Encrypt)

Cloud Run tự động cung cấp SSL certificate cho custom domain.

### Scaling Configuration

```bash
# Update scaling
gcloud run services update swinx-app \
  --region=asia-southeast1 \
  --min-instances=1 \
  --max-instances=20 \
  --cpu=2 \
  --memory=2Gi
```

### Environment Variables

```bash
# Update environment variables
gcloud run services update swinx-app \
  --region=asia-southeast1 \
  --set-env-vars="NEW_VAR=value"
```

---

## 📚 Resources

- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
- [GitHub Actions for Google Cloud](https://github.com/google-github-actions)
- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [Laravel Deployment Guide](https://laravel.com/docs/deployment)

---

## 📞 Support

Nếu gặp issues:
1. Check logs trong GitHub Actions
2. Check Cloud Run logs
3. Test Docker image locally
4. Verify environment variables và secrets 

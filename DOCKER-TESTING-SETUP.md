# 🐳🧪 Docker Compose Testing Setup - Complete Guide

## 📋 Overview

This document provides a comprehensive overview of the Docker Compose testing setup that ensures all local testing passes before pushing to GitHub. The setup mirrors the GitHub Actions CI/CD pipeline and provides a reliable local development and testing environment.

## 🎯 Goals Achieved

✅ **Reliable Local Testing Environment**
- Docker Compose setup with MySQL database (matches production)
- Isolated test environment separate from development
- Comprehensive testing pipeline that mirrors GitHub Actions

✅ **Pre-push Validation**
- Enhanced pre-push script with Docker integration
- Comprehensive code quality checks
- Database integration testing

✅ **Complete CI/CD Mirror**
- Same PHP/Node.js versions as GitHub Actions
- Same dependency installation process
- Same test execution environment

✅ **Developer Experience**
- Simple commands for complex operations
- Clear error messages and troubleshooting
- Fast feedback loop for development

## 🏗️ Architecture

### Docker Services

1. **app** - Main Laravel application (development)
2. **app-test** - Laravel application for testing
3. **db** - MySQL 8.0 database (development)
4. **test-db** - MySQL 8.0 database (testing)
5. **redis** - Redis cache and queue storage
6. **phpmyadmin** - Database management interface

### Testing Pipeline Phases

1. **Environment Setup** - Validate system and prepare environment
2. **Docker Environment** - Start and validate Docker services
3. **Dependency Management** - Install PHP and Node.js dependencies
4. **Application Setup** - Configure Laravel, run migrations, seed data
5. **Frontend Build** - Compile and optimize frontend assets
6. **Code Quality** - Run linting, formatting, and style checks
7. **Testing** - Execute unit, feature, and integration tests
8. **Integration Validation** - Test database connectivity and endpoints
9. **Security & Performance** - Validate security and performance metrics
10. **Final Validation** - Comprehensive system check

## 🚀 Quick Start Commands

### Initial Setup
```bash
# Complete project setup (run once)
npm run setup
# or
./scripts/setup-project.sh
```

### Daily Development
```bash
# Start development environment
npm run docker:up

# Run comprehensive tests
npm run test:local

# Pre-push validation
npm run pre-push:docker

# Stop environment
npm run docker:down
```

### Validation and Troubleshooting
```bash
# Validate system setup
npm run validate

# View logs
npm run docker:logs

# Reset environment
./scripts/docker-compose-dev.sh clean
```

## 📁 File Structure

### New Files Added
```
├── docker-compose.test.yml          # Test environment configuration
├── docker/mysql/init-test.sql       # Test database initialization
├── scripts/
│   ├── test-local.sh               # Comprehensive testing script
│   ├── validate-setup.sh           # System validation script
│   └── setup-project.sh            # Complete project setup
├── tests/Feature/
│   ├── HealthCheckTest.php         # Health and connectivity tests
│   └── DockerIntegrationTest.php   # Docker environment tests
├── TESTING.md                      # Comprehensive testing guide
└── DOCKER-TESTING-SETUP.md        # This document
```

### Enhanced Files
```
├── docker-compose.yml              # Added test database service
├── scripts/
│   ├── docker-compose-dev.sh      # Added test commands
│   └── pre-push.sh                 # Added Docker testing option
├── package.json                    # Added new npm scripts
└── README.md                       # Updated with testing info
```

## 🔧 Configuration Details

### Test Database Configuration
- **Host**: localhost:3307
- **Database**: swinburne_test
- **Username**: swinx_test_user
- **Password**: swinx_test_password
- **Optimizations**: tmpfs storage, reduced buffer sizes

### Environment Variables (Testing)
```bash
APP_ENV=testing
APP_DEBUG=true
DB_CONNECTION=mysql
DB_HOST=test-db
DB_DATABASE=swinburne_test
CACHE_DRIVER=array
SESSION_DRIVER=array
QUEUE_CONNECTION=sync
```

### Performance Optimizations
- **tmpfs Database Storage**: Fast in-memory database for tests
- **Optimized MySQL Configuration**: Reduced memory usage for testing
- **Parallel Service Startup**: Services start concurrently
- **Layer Caching**: Docker layer caching for faster builds

## 🧪 Testing Strategy

### Test Types
1. **Unit Tests** - Individual component testing
2. **Feature Tests** - Complete workflow testing
3. **Integration Tests** - Database and service integration
4. **Health Checks** - System connectivity and status
5. **Performance Tests** - Response time and resource usage
6. **Security Tests** - Debug statement detection, CSRF validation

### Test Environments
- **SQLite (Fast)** - For quick unit and feature tests
- **MySQL (Integration)** - For comprehensive integration testing
- **Docker (Production-like)** - For complete system validation

## 🔍 Validation Checks

### System Requirements
- Docker and Docker Compose installation
- Port availability (8080, 3306, 3307, 6379, 8081)
- Sufficient disk space (2GB+) and memory (1GB+)
- Required project files and directories

### Code Quality
- PHP code style (Laravel Pint)
- Frontend linting (ESLint)
- Code formatting (Prettier)
- TypeScript type checking
- Debug statement detection

### Application Health
- Database connectivity
- Health endpoint accessibility
- Route generation
- Asset compilation
- Migration execution

## 🐛 Troubleshooting

### Common Issues and Solutions

#### Port Conflicts
```bash
# Check port usage
lsof -i :8080
lsof -i :3306

# Stop conflicting services
sudo service apache2 stop
sudo service mysql stop
```

#### Docker Issues
```bash
# Reset Docker environment
./scripts/docker-compose-dev.sh clean

# Rebuild containers
docker-compose build --no-cache

# Check Docker daemon
docker info
```

#### Database Issues
```bash
# Reset test database
docker-compose -f docker-compose.yml -f docker-compose.test.yml down -v
docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d

# Check database logs
./scripts/docker-compose-dev.sh logs db
```

#### Test Failures
```bash
# Run specific test
./scripts/docker-compose-dev.sh test tests/Feature/ExampleTest.php

# Debug in container
./scripts/docker-compose-dev.sh shell app-test
```

## 📊 Performance Metrics

### Expected Performance
- **Container Startup**: < 60 seconds
- **Test Execution**: < 5 minutes
- **Database Operations**: < 100ms per query
- **Health Checks**: < 1 second response time

### Resource Usage
- **Memory**: ~1GB total for all containers
- **CPU**: Moderate during builds, low during runtime
- **Disk**: ~2GB for images and volumes
- **Network**: Isolated Docker network

## 🔒 Security Considerations

### Development Security
- Isolated Docker networks
- Non-root container users where possible
- Environment variable validation
- Debug statement detection

### Production Readiness
- Same environment as production
- Security header validation
- CSRF protection testing
- Input validation testing

## 🚀 Future Enhancements

### Planned Improvements
1. **Browser Testing** - Laravel Dusk integration
2. **Performance Testing** - Load testing with Apache Bench
3. **Security Scanning** - Automated vulnerability scanning
4. **Code Coverage** - Test coverage reporting
5. **Parallel Testing** - Faster test execution

### Monitoring Integration
1. **Error Tracking** - Sentry integration
2. **Performance Monitoring** - Application performance metrics
3. **Log Aggregation** - Centralized logging
4. **Health Monitoring** - Automated health checks

## 📚 Related Documentation

- **[🐳 Docker Compose Guide](DOCKER-COMPOSE.md)** - Complete Docker setup
- **[🧪 Testing Guide](TESTING.md)** - Detailed testing procedures
- **[🚀 Deployment Guide](DEPLOYMENT.md)** - Production deployment
- **[📋 Development Guidelines](docs/DEVELOPMENT_CHECKLIST_AND_GUIDELINES.md)** - Code standards

## 🎉 Success Metrics

The setup is considered successful when:

✅ All system validations pass
✅ Docker containers start without errors
✅ Database connections are established
✅ All tests pass in under 5 minutes
✅ Code quality checks pass
✅ Health endpoints respond correctly
✅ Pre-push validation completes successfully

## 💡 Best Practices

### For Developers
1. **Run tests before committing** - Use `npm run pre-push:docker`
2. **Keep tests fast** - Use appropriate test types
3. **Monitor resource usage** - Don't leave containers running unnecessarily
4. **Update tests with code changes** - Maintain test coverage

### For Maintenance
1. **Regular updates** - Keep Docker images updated
2. **Monitor performance** - Track test execution times
3. **Clean up regularly** - Remove unused Docker resources
4. **Document changes** - Update documentation with modifications

---

This comprehensive Docker Compose testing setup ensures reliable local development and testing that mirrors the production CI/CD pipeline, preventing GitHub Actions failures and deployment issues by catching all problems locally first.

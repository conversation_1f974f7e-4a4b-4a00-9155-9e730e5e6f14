# Git
.git
.gitignore
.github

# Environment
.env
.env.*
!.env.example

# Dependencies
node_modules
vendor

# Build artifacts
public/build
public/hot
public/storage

# Logs
storage/logs/*
storage/framework/sessions/*
storage/framework/views/*
storage/framework/cache/*
!storage/logs/.gitkeep
!storage/framework/sessions/.gitkeep
!storage/framework/views/.gitkeep
!storage/framework/cache/.gitkeep

# Testing
coverage
.phpunit.result.cache
phpunit.xml

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Docker
docker-compose*.yml
Dockerfile*

# Documentation
README.md
*.md

# Scripts (production không cần)
scripts/

# Development tools
.prettierrc
eslint.config.js
tsconfig.json
tailwind.config.js
vite.config.ts

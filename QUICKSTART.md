# 🚀 Quick Start Guide

## ✅ Quy Trình Test Trước Khi Push

### Cách 1: Sử dụng Scrip<PERSON> (Khuyến nghị)

```bash
# Chạy tất cả tests
./scripts/pre-push.sh

# Hoặc sử dụng npm
npm run pre-push

# Hoặc sử dụng composer
composer pre-push
```

### Cách 2: Ch<PERSON>y Từng Bư<PERSON>

```bash
# 1. Build frontend
npm run build

# 2. Test PHP code style
vendor/bin/pint --test

# 3. Test frontend
npm run lint
npm run format:check
npm run type-check

# 4. Run PHP tests
./vendor/bin/pest
```

### 🐳 Docker Architecture (Simplified)

### Development: Docker Compose
- **Preferred method** cho local development
- Hot reload, multiple services (MySQL, Redis, phpMyAdmin)
- Dễ dàng debug và development

### Production: Dockerfile 
- **Chỉ dành cho GitHub Actions** và production deployment
- Single container optimized cho Cloud Run
- Tự động build qua CI/CD pipeline

```bash
# ❌ KHÔNG cần manual Docker builds
npm run docker:build   # (Đ<PERSON> xóa - không cần thiết)

# ✅ Chỉ dùng Docker Compose
npm run docker:up      # Development với Docker Compose
composer dev           # Local development (khuyến nghị)
```

---

## 🔄 Workflow Hàng Ngày

### 1. Development

```bash
# Tạo feature branch
git checkout -b feature/your-feature

# Code...
# Test trước khi commit
npm run pre-push  # ✅ Phải PASS

# Commit và push
git add .
git commit -m "feat: your feature"
git push origin feature/your-feature
```

### 2. Pull Request

- Tạo PR trên GitHub
- GitHub Actions sẽ tự động chạy tests
- Merge sau khi tests pass và code review

### 3. Deploy

```bash
# Push lên main branch
git checkout main
git merge feature/your-feature
git push origin main

# 🚀 Auto deploy lên Cloud Run!
```

---

## 📋 Checklist Nhanh

### ✅ Trước Khi Commit

- [ ] `npm run pre-push` PASS
- [ ] Không có `console.log` hoặc `dd()`
- [ ] Code được format đúng
- [ ] Tests pass

### ✅ Trước Khi Deploy

- [ ] GitHub secrets configured
- [ ] Google Cloud setup complete
- [ ] Database migrations ready
- [ ] Environment variables updated

---

## 🆘 Commands Hữu Ích

```bash
# Development
composer dev              # Start dev server với tất cả services
npm run dev               # Frontend dev server
php artisan serve         # Laravel server

# Testing
npm run pre-push          # All tests
composer ci               # CI tests only

# Code Quality
vendor/bin/pint           # Fix PHP code style
npm run format            # Fix frontend format
npm run lint              # Fix linting issues

# Docker Compose
npm run docker:up         # Start development environment
npm run docker:down       # Stop environment
npm run docker:logs       # View logs

# Production Docker (chỉ khi cần)
docker build -t swinx-app:latest .  # Build production image

# Laravel
php artisan migrate       # Run migrations
php artisan db:seed       # Seed database
php artisan ziggy:generate  # Generate routes for frontend
```

---

## 🔗 Files Quan Trọng

- [`DOCKER-COMPOSE.md`](./DOCKER-COMPOSE.md) - Docker Compose development guide
- [`DEPLOYMENT.md`](./DEPLOYMENT.md) - Hướng dẫn chi tiết deploy
- [`scripts/pre-push.sh`](./scripts/pre-push.sh) - Script test local
- [`scripts/docker-compose-dev.sh`](./scripts/docker-compose-dev.sh) - Docker Compose management
- [`Dockerfile`](./Dockerfile) - Docker configuration
- [`.github/workflows/deploy.yml`](./.github/workflows/deploy.yml) - Auto deploy workflow

---

⚡ **TIP**: Luôn chạy `npm run pre-push` trước khi push code! 

import { ref } from 'vue'

export interface DeleteConfirmationOptions {
  title?: string
  message: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
}

export function useDeleteConfirmation() {
  const isOpen = ref(false)
  const isLoading = ref(false)
  const options = ref<DeleteConfirmationOptions | null>(null)

  const showConfirmation = (confirmationOptions: DeleteConfirmationOptions) => {
    options.value = {
      title: 'Confirm Delete',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      ...confirmationOptions,
    }
    isOpen.value = true
  }

  const handleConfirm = async () => {
    if (!options.value) return

    try {
      isLoading.value = true
      await options.value.onConfirm()
      closeDialog()
    } catch (error) {
      console.error('Delete confirmation error:', error)
      // Keep dialog open on error so user can retry or cancel
    } finally {
      isLoading.value = false
    }
  }

  const handleCancel = () => {
    if (options.value?.onCancel) {
      options.value.onCancel()
    }
    closeDialog()
  }

  const closeDialog = () => {
    isOpen.value = false
    isLoading.value = false
    options.value = null
  }

  return {
    isOpen,
    isLoading,
    options,
    showConfirmation,
    handleConfirm,
    handleCancel,
    closeDialog,
  }
}

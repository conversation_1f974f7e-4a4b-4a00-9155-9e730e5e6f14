<script setup lang="ts">
import DeleteConfirmationDialog from '@/components/DeleteConfirmationDialog.vue';
import { Button } from '@/components/ui/button';
import { useDeleteConfirmation } from '@/composables';
import { ref } from 'vue';

// Example data
const items = ref([
    { id: 1, name: '<PERSON>', type: 'user' },
    { id: 2, name: '<PERSON>', type: 'user' },
    { id: 3, name: 'Sample Project', type: 'project' },
]);

// Initialize delete confirmation composable
const deleteConfirmation = useDeleteConfirmation();

// Example delete handlers for different entity types
const deleteUser = (user: { id: number; name: string }) => {
    deleteConfirmation.showConfirmation({
        title: 'Delete User',
        message: `Are you sure you want to delete ${user.name}? This will permanently remove their account and all associated data.`,
        confirmText: 'Delete User',
        onConfirm: async () => {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Remove from local data
            items.value = items.value.filter((item) => item.id !== user.id);

            console.log(`User ${user.name} deleted successfully`);
        },
        onCancel: () => {
            console.log('User deletion cancelled');
        },
    });
};

const deleteProject = (project: { id: number; name: string }) => {
    deleteConfirmation.showConfirmation({
        title: 'Delete Project',
        message: `Are you sure you want to delete "${project.name}"? This action cannot be undone and will remove all project files and settings.`,
        confirmText: 'Delete Project',
        cancelText: 'Keep Project',
        onConfirm: async () => {
            // Simulate API call with potential error
            await new Promise((resolve, reject) => {
                setTimeout(() => {
                    // Simulate random success/failure
                    if (Math.random() > 0.5) {
                        resolve(undefined);
                    } else {
                        reject(new Error('Failed to delete project'));
                    }
                }, 1500);
            });

            // Remove from local data
            items.value = items.value.filter((item) => item.id !== project.id);

            console.log(`Project ${project.name} deleted successfully`);
        },
    });
};

// Quick delete with minimal configuration
const quickDelete = (item: { id: number; name: string }) => {
    deleteConfirmation.showConfirmation({
        message: `Delete ${item.name}?`,
        onConfirm: async () => {
            await new Promise((resolve) => setTimeout(resolve, 500));
            items.value = items.value.filter((i) => i.id !== item.id);
        },
    });
};
</script>

<template>
    <div class="space-y-6 p-6">
        <div>
            <h2 class="mb-4 text-2xl font-bold">Delete Confirmation Examples</h2>
            <p class="mb-6 text-gray-600">This demonstrates the reusable delete confirmation composable and component.</p>
        </div>

        <!-- Examples List -->
        <div class="space-y-4">
            <div v-for="item in items" :key="item.id" class="flex items-center justify-between rounded-lg border p-4">
                <div>
                    <h3 class="font-medium">{{ item.name }}</h3>
                    <p class="text-sm text-gray-500 capitalize">{{ item.type }}</p>
                </div>

                <div class="flex gap-2">
                    <!-- Context-aware delete buttons -->
                    <Button v-if="item.type === 'user'" variant="destructive" size="sm" @click="deleteUser(item)"> Delete User </Button>

                    <Button v-if="item.type === 'project'" variant="destructive" size="sm" @click="deleteProject(item)"> Delete Project </Button>

                    <!-- Quick delete option -->
                    <Button variant="outline" size="sm" @click="quickDelete(item)"> Quick Delete </Button>
                </div>
            </div>
        </div>

        <!-- Usage Information -->
        <div class="mt-8 rounded-lg bg-blue-50 p-4">
            <h3 class="mb-2 font-medium text-blue-900">Usage Examples:</h3>
            <ul class="space-y-1 text-sm text-blue-800">
                <li>• <strong>User Delete:</strong> Custom title, detailed message, custom button text</li>
                <li>• <strong>Project Delete:</strong> Error handling, custom cancel text, longer loading time</li>
                <li>• <strong>Quick Delete:</strong> Minimal configuration with defaults</li>
            </ul>
        </div>

        <!-- Delete Confirmation Dialog -->
        <DeleteConfirmationDialog
            :open="deleteConfirmation.isOpen.value"
            :loading="deleteConfirmation.isLoading.value"
            :options="deleteConfirmation.options.value"
            @confirm="deleteConfirmation.handleConfirm"
            @cancel="deleteConfirmation.handleCancel"
            @update:open="deleteConfirmation.closeDialog"
        />
    </div>
</template>

# 🐳 Docker Compose Development Guide

## 📋 Tổng Quan

Docker Compose setup bao gồm:
- **<PERSON><PERSON> App** - Ứng dụng chính với Nginx + PHP-FPM
- **MySQL 8.0** - Database chính
- **Redis** - Cache và Queue
- **phpMyAdmin** - Database management (dev only)

### 🔍 Docker vs Docker Compose

- **Docker Compose** (Development): Dành cho local development với hot reload, multiple services
- **Dockerfile** (Production): Chỉ dành cho production deployment lên Cloud Run/Kubernetes

```bash
# Development: Sử dụng Docker Compose
npm run docker:up

# Production: GitHub Actions tự động build từ Dockerfile
# Không cần manual build trừ khi test production image
```

---

## 🚀 Quick Start

### 1. Setup Môi Trường

```bash
# Copy environment file
cp env.docker.example .env

# Generate application key (nếu có PHP local)
php artisan key:generate

# Start development environment
./scripts/docker-compose-dev.sh up
```

### 2. T<PERSON><PERSON>p Services

- **🌐 Application**: http://localhost:8080
- **🗄️ phpMyAdmin**: http://localhost:8081
- **📊 MySQL**: localhost:3306
- **🔴 Redis**: localhost:6379

### 3. Credentials

```bash
# MySQL
Host: localhost:3306
Database: swinburne
Username: swinx_user
Password: swinx_password

# MySQL Root
Username: root
Password: root_password

# phpMyAdmin
Same as MySQL credentials above
```

---

## 🔧 Management Commands

### Development Script (Khuyến nghị)

```bash
# Start development environment
./scripts/docker-compose-dev.sh up
npm run docker:up

# Stop environment
./scripts/docker-compose-dev.sh down
npm run docker:down

# View logs
./scripts/docker-compose-dev.sh logs
./scripts/docker-compose-dev.sh logs app  # Specific service
npm run docker:logs

# Restart services
./scripts/docker-compose-dev.sh restart

# Build containers
./scripts/docker-compose-dev.sh build
```

### Laravel Commands

```bash
# Run artisan commands
./scripts/docker-compose-dev.sh artisan migrate
./scripts/docker-compose-dev.sh artisan db:seed
./scripts/docker-compose-dev.sh artisan migrate:fresh --seed

# Open shell in app container
./scripts/docker-compose-dev.sh shell
./scripts/docker-compose-dev.sh shell app

# Run tests
./scripts/docker-compose-dev.sh test

# Composer commands
./scripts/docker-compose-dev.sh composer install
./scripts/docker-compose-dev.sh composer update
```

### Database Commands

```bash
# Open MySQL client
./scripts/docker-compose-dev.sh mysql

# Fresh database
./scripts/docker-compose-dev.sh fresh  # migrate:fresh --seed
```

### Utility Commands

```bash
# Container status
./scripts/docker-compose-dev.sh status

# Clean up everything
./scripts/docker-compose-dev.sh clean

# Help
./scripts/docker-compose-dev.sh help
```

---

## 📁 File Structure

```
├── docker-compose.yml          # Base configuration
├── docker-compose.dev.yml      # Development overrides
├── docker-compose.prod.yml     # Production overrides
├── env.docker.example          # Environment template
├── docker/
│   ├── nginx/                  # Nginx configuration
│   ├── php/                    # PHP configuration
│   ├── mysql/                  # MySQL configuration
│   ├── supervisord.conf        # Process manager
│   └── start.sh                # Container startup script
└── scripts/
    └── docker-compose-dev.sh   # Management script
```

---

## 🔄 Development Workflow

### 1. Daily Development

```bash
# Start environment
npm run docker:up

# Code changes (auto-reload với volume mount)
# Edit files in IDE

# Run tests
./scripts/docker-compose-dev.sh test

# View logs
npm run docker:logs

# Stop when done
npm run docker:down
```

### 2. Database Operations

```bash
# Run migrations
./scripts/docker-compose-dev.sh artisan migrate

# Seed data
./scripts/docker-compose-dev.sh artisan db:seed

# Fresh database
./scripts/docker-compose-dev.sh fresh

# Access database directly
./scripts/docker-compose-dev.sh mysql
```

### 3. Frontend Development

**Lưu ý**: Frontend development tốt nhất chạy ngoài container để có HMR nhanh.

```bash
# Option 1: Chạy frontend ngoài container (khuyến nghị)
npm run dev  # Chạy trên host

# Option 2: Chạy trong container (nếu cần)
./scripts/docker-compose-dev.sh npm install
./scripts/docker-compose-dev.sh npm run build
```

---

## 🏗️ Docker Compose Configuration

### Base Configuration (docker-compose.yml)

- **App Service**: Laravel application với Nginx + PHP-FPM
- **Database Service**: MySQL 8.0 với custom configuration
- **Redis Service**: Cache và queue storage
- **Networks**: Isolated network cho services
- **Volumes**: Persistent data storage

### Development Overrides (docker-compose.dev.yml)

- **Volume Mounts**: Source code mount cho hot reload
- **Environment**: Development settings
- **phpMyAdmin**: Database management tool
- **Debug Mode**: Enabled logging và debugging

### Production Overrides (docker-compose.prod.yml)

- **No Volume Mounts**: Code trong image
- **Resource Limits**: CPU và memory constraints
- **Security**: Không expose ports không cần thiết
- **Performance**: Optimized settings

---

## 🔧 Customization

### Environment Variables

Edit `.env` file để thay đổi:

```bash
# Database settings
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Application settings
APP_NAME="Your App Name"
APP_URL=http://localhost:8080

# Cache settings
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

### Port Configuration

Edit `docker-compose.yml`:

```yaml
services:
  app:
    ports:
      - "8080:8080"  # Change first port for host

  db:
    ports:
      - "3306:3306"  # Change for different MySQL port

  phpmyadmin:
    ports:
      - "8081:80"    # Change phpMyAdmin port
```

### MySQL Configuration

Edit `docker/mysql/my.cnf`:

```ini
[mysqld]
# Add custom MySQL settings
max_connections=300
innodb_buffer_pool_size=512M
```

---

## 🐛 Troubleshooting

### Common Issues

#### 1. Port Already in Use

```bash
# Check what's using the port
lsof -i :8080
lsof -i :3306

# Stop conflicting services
sudo service apache2 stop
sudo service mysql stop

# Or change ports in docker-compose.yml
```

#### 2. Database Connection Issues

```bash
# Check database container
./scripts/docker-compose-dev.sh logs db

# Test connection
./scripts/docker-compose-dev.sh mysql

# Reset database
./scripts/docker-compose-dev.sh down
docker volume rm swinx_mysql_data
./scripts/docker-compose-dev.sh up
```

#### 3. Application Not Loading

```bash
# Check app logs
./scripts/docker-compose-dev.sh logs app

# Check container status
./scripts/docker-compose-dev.sh status

# Rebuild containers
./scripts/docker-compose-dev.sh build
./scripts/docker-compose-dev.sh up
```

#### 4. Permission Issues

```bash
# Fix storage permissions
./scripts/docker-compose-dev.sh shell
chown -R www-data:www-data /var/www/html/storage
chmod -R 775 /var/www/html/storage
```

### Debug Commands

```bash
# Enter container shell
./scripts/docker-compose-dev.sh shell app

# Check PHP configuration
./scripts/docker-compose-dev.sh shell app
php -m  # Check modules
php --ini  # Check config files

# Check Nginx configuration
./scripts/docker-compose-dev.sh shell app
nginx -t

# Check database connection
./scripts/docker-compose-dev.sh shell app
php artisan tinker
DB::connection()->getPdo();
```

---

## 🚀 Production Deployment

### Using Production Compose

```bash
# Build production image
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Deploy to production server
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Check status
docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps
```

### Environment Setup

```bash
# Set production environment variables
export DB_PASSWORD=your-secure-password
export APP_KEY=your-app-key
export DB_HOST=your-production-db-host

# Deploy
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

---

## 📚 Best Practices

### 1. Security

- **Passwords**: Sử dụng strong passwords cho production
- **Networks**: Sử dụng custom networks
- **Secrets**: Sử dụng Docker secrets cho sensitive data
- **Firewall**: Chỉ expose ports cần thiết

### 2. Performance

- **Resource Limits**: Set memory và CPU limits
- **Volume Optimization**: Sử dụng named volumes
- **Image Optimization**: Multi-stage builds
- **Caching**: Leverage Docker layer caching

### 3. Development

- **Hot Reload**: Volume mounts cho development
- **Debugging**: Enable debug mode và logging
- **Testing**: Automated testing trong containers
- **Consistency**: Sử dụng same versions như production

### 4. Monitoring

- **Health Checks**: Configure container health checks
- **Logging**: Centralized logging strategy
- **Metrics**: Monitor resource usage
- **Alerts**: Set up alerts cho critical issues

---

## 🔗 Related Files

- [`docker-compose.yml`](./docker-compose.yml) - Base configuration
- [`docker-compose.dev.yml`](./docker-compose.dev.yml) - Development overrides
- [`docker-compose.prod.yml`](./docker-compose.prod.yml) - Production overrides
- [`env.docker.example`](./env.docker.example) - Environment template
- [`scripts/docker-compose-dev.sh`](./scripts/docker-compose-dev.sh) - Management script
- [`Dockerfile`](./Dockerfile) - Application image definition

---

## 💡 Tips

- **Sử dụng script**: `./scripts/docker-compose-dev.sh` cho mọi operations
- **Check logs**: Luôn check logs khi có issues
- **Clean rebuild**: Khi có issues, thử clean build
- **Environment consistency**: Giữ .env file updated
- **Volume persistence**: Database data được persist qua restarts 

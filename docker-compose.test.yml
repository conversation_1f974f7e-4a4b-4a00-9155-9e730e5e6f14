services:
  # Test application service
  app-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: php-base
    container_name: swinx-app-test
    environment:
      - APP_ENV=testing
      - APP_DEBUG=true
      - APP_KEY=base64:dGVzdGluZ2tleWZvcnN3aW54YXBwbGljYXRpb24=
      - DB_CONNECTION=mysql
      - DB_HOST=test-db
      - DB_PORT=3306
      - DB_DATABASE=swinburne_test
      - DB_USERNAME=swinx_test_user
      - DB_PASSWORD=swinx_test_password
      - CACHE_DRIVER=array
      - SESSION_DRIVER=array
      - QUEUE_CONNECTION=sync
      - MAIL_MAILER=array
      - LOG_CHANNEL=stderr
      - LOG_LEVEL=debug
    volumes:
      - .:/var/www/html
      - /var/www/html/node_modules
      - /var/www/html/vendor
    depends_on:
      test-db:
        condition: service_healthy
    networks:
      - swinx-network
    command: >
      sh -c "
        echo 'Waiting for test database...' &&
        sleep 5 &&
        php artisan config:clear &&
        php artisan cache:clear &&
        php artisan migrate:fresh --force &&
        php artisan db:seed --force &&
        echo 'Test environment ready!' &&
        tail -f /dev/null
      "

  # Override test database configuration
  test-db:
    environment:
      MYSQL_ROOT_PASSWORD: test_root_password
      MYSQL_DATABASE: swinburne_test
      MYSQL_USER: swinx_test_user
      MYSQL_PASSWORD: swinx_test_password
    tmpfs:
      - /var/lib/mysql:rw,noexec,nosuid,size=1g
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=128M
      --innodb-log-file-size=32M
      --innodb-flush-log-at-trx-commit=0
      --sync-binlog=0
      --innodb-doublewrite=0

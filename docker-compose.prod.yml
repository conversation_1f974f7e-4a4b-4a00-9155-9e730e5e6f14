services:
  # Production overrides
  app:
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=${DB_HOST:-db}
      - DB_DATABASE=${DB_DATABASE:-swinburne}
      - DB_USERNAME=${DB_USERNAME:-swinx_user}
      - DB_PASSWORD=${DB_PASSWORD}
      - APP_KEY=${APP_KEY}
      - LOG_CHANNEL=stderr
      - SESSION_DRIVER=database
      - CACHE_DRIVER=redis
      - QUEUE_CONNECTION=redis
      - REDIS_HOST=redis
    # Remove volume mounts for production (code is in image)
    volumes: []
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1'
        reservations:
          memory: 512M
          cpus: '0.5'

  db:
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE:-swinburne}
      MYSQL_USER: ${DB_USERNAME:-swinx_user}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    # Don't expose ports in production
    ports: []
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  redis:
    restart: always
    # Don't expose ports in production
    ports: []
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Disable phpMyAdmin in production
  phpmyadmin:
    deploy:
      replicas: 0

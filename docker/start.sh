#!/bin/sh

# Exit on any error
set -e

echo "Starting Laravel application..."

# Wait for database connection if DB_HOST is set
if [ ! -z "$DB_HOST" ]; then
    echo "Waiting for database connection..."
    until php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1; do
        echo "Database not ready, waiting..."
        sleep 2
    done
    echo "Database connection established!"
fi

# Clear caches
echo "Clearing application caches..."
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# Cache configurations for production
echo "Caching configurations..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Generate Ziggy routes
echo "Generating Ziggy routes..."
php artisan ziggy:generate

# Set proper permissions
echo "Setting file permissions..."
chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

echo "Application ready! Starting services..."

# Start PHP-FPM in background
echo "Starting PHP-FPM..."
php-fpm -D

# Start Nginx in foreground (this keeps container running)
echo "Starting Nginx..."
exec nginx -g "daemon off;"

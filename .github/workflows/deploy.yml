name: Deploy to Google Cloud Run

on:
  push:
    branches: [main]
  workflow_dispatch:

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  SERVICE_NAME: swinx-app
  REGION: asia-southeast1

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install Dependencies
        run: |
          composer install --no-interaction --prefer-dist --optimize-autoloader
          npm ci

      - name: Copy Environment File
        run: cp .env.example .env

      - name: Generate Application Key
        run: php artisan key:generate

      - name: Publish Ziggy Configuration
        run: php artisan ziggy:generate

      - name: Build Assets
        run: npm run build

      - name: Run Tests
        run: ./vendor/bin/pest

      - name: Run Linting
        run: |
          vendor/bin/pint --test
          npm run lint
          npm run format:check

  deploy:
    needs: test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker $REGION-docker.pkg.dev

      - name: Build and Push Docker Image
        run: |
          IMAGE_URI=$REGION-docker.pkg.dev/$PROJECT_ID/swinx/$SERVICE_NAME:$GITHUB_SHA

          # Build image
          docker build -t $IMAGE_URI .

          # Push image
          docker push $IMAGE_URI

          echo "IMAGE_URI=$IMAGE_URI" >> $GITHUB_ENV

      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy $SERVICE_NAME \
            --image $IMAGE_URI \
            --platform managed \
            --region $REGION \
            --allow-unauthenticated \
            --port 8080 \
            --memory 1Gi \
            --cpu 1 \
            --min-instances 0 \
            --max-instances 10 \
            --set-env-vars APP_ENV=production \
            --set-env-vars APP_DEBUG=false \
            --set-env-vars LOG_CHANNEL=stderr \
            --set-secrets APP_KEY=app-key:latest \
            --set-secrets DB_HOST=db-host:latest \
            --set-secrets DB_DATABASE=db-database:latest \
            --set-secrets DB_USERNAME=db-username:latest \
            --set-secrets DB_PASSWORD=db-password:latest

      - name: Get Service URL
        run: |
          SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
          echo "🚀 Application deployed to: $SERVICE_URL"

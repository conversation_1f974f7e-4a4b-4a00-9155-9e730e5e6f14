# 🧪 Comprehensive Testing Guide

## 📋 Overview

This guide covers the complete testing setup for the Swinx application, including local Docker-based testing that mirrors the GitHub Actions CI/CD pipeline.

## 🎯 Testing Philosophy

- **Test Early, Test Often**: Run tests before every commit
- **Mirror Production**: Use MySQL database for integration tests
- **Comprehensive Coverage**: Unit, Feature, Integration, and E2E tests
- **Fast Feedback**: Quick local testing with Docker Compose

---

## 🚀 Quick Start

### 1. Fast Local Testing (SQLite)
```bash
# Quick tests using SQLite (matches GitHub Actions)
./scripts/pre-push.sh
```

### 2. Comprehensive Docker Testing (MySQL)
```bash
# Full integration testing with MySQL database
./scripts/test-local.sh

# Or using npm script
npm run test:local
```

### 3. Pre-push with Docker Testing
```bash
# Enhanced pre-push with Docker integration tests
./scripts/pre-push.sh --docker
npm run pre-push:docker
```

---

## 🐳 Docker Test Environment

### Architecture
- **app-test**: Laravel application container for testing
- **test-db**: MySQL 8.0 database with test data
- **Isolated Network**: Separate from development environment
- **Temporary Storage**: Fast tmpfs for database performance

### Services
- **Test Application**: http://localhost:8080 (when running)
- **Test Database**: localhost:3307
- **Test Database Name**: `swinburne_test`
- **Test User**: `swinx_test_user` / `swinx_test_password`

---

## 🔧 Testing Commands

### Development Script Commands
```bash
# Setup test environment
./scripts/docker-compose-dev.sh test:setup

# Run comprehensive tests
./scripts/docker-compose-dev.sh test:full

# Stop test environment
./scripts/docker-compose-dev.sh test:down

# Quick tests in dev environment
./scripts/docker-compose-dev.sh test
```

### Direct Docker Compose Commands
```bash
# Start test environment
docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d

# Run tests in container
docker-compose -f docker-compose.yml -f docker-compose.test.yml exec app-test ./vendor/bin/pest

# Stop test environment
docker-compose -f docker-compose.yml -f docker-compose.test.yml down -v
```

### NPM Scripts
```bash
npm run test:local          # Run comprehensive local tests
npm run pre-push:docker     # Pre-push with Docker testing
npm run docker:test         # Start test environment
npm run docker:test:down    # Stop test environment
```

---

## 📊 Test Types & Coverage

### 1. Unit Tests (`tests/Unit/`)
- **Purpose**: Test individual classes and methods
- **Database**: Not required
- **Speed**: Very fast
- **Coverage**: Models, Services, Helpers

### 2. Feature Tests (`tests/Feature/`)
- **Purpose**: Test complete user workflows
- **Database**: SQLite (fast) or MySQL (integration)
- **Speed**: Fast to moderate
- **Coverage**: Controllers, API endpoints, Authentication

### 3. Integration Tests
- **Purpose**: Test component interactions
- **Database**: MySQL (matches production)
- **Speed**: Moderate
- **Coverage**: Database operations, External services

### 4. Browser Tests (Future)
- **Purpose**: End-to-end user scenarios
- **Tools**: Laravel Dusk
- **Coverage**: Complete user journeys

---

## 🔍 Test Pipeline Phases

### Phase 1: Environment Setup
- ✅ Environment file validation
- ✅ Docker environment preparation
- ✅ Service health checks

### Phase 2: Dependency Management
- ✅ PHP dependency installation
- ✅ Node.js dependency installation
- ✅ Dependency security audit

### Phase 3: Application Setup
- ✅ Cache clearing
- ✅ Key generation
- ✅ Database migrations
- ✅ Test data seeding
- ✅ Route generation

### Phase 4: Frontend Build
- ✅ Asset compilation
- ✅ TypeScript compilation
- ✅ Build optimization

### Phase 5: Code Quality
- ✅ PHP code style (Pint)
- ✅ Frontend linting (ESLint)
- ✅ Code formatting (Prettier)
- ✅ TypeScript type checking

### Phase 6: Testing
- ✅ Unit tests
- ✅ Feature tests
- ✅ Integration tests

### Phase 7: Integration Validation
- ✅ Database connectivity
- ✅ Health endpoint testing
- ✅ Application accessibility

### Phase 8: Security & Performance
- ✅ Debug statement detection
- ✅ Environment variable validation
- ✅ Resource usage monitoring

---

## 🛠️ Configuration Files

### Test Environment Configuration
- `docker-compose.test.yml` - Test service definitions
- `docker/mysql/init-test.sql` - Test database initialization
- `phpunit.xml` - PHPUnit configuration
- `tests/Pest.php` - Pest testing framework setup

### Environment Variables (Testing)
```bash
APP_ENV=testing
DB_CONNECTION=mysql
DB_HOST=test-db
DB_DATABASE=swinburne_test
DB_USERNAME=swinx_test_user
DB_PASSWORD=swinx_test_password
CACHE_DRIVER=array
SESSION_DRIVER=array
QUEUE_CONNECTION=sync
```

---

## 🐛 Troubleshooting

### Common Issues

#### 1. Docker Container Won't Start
```bash
# Check Docker status
docker ps -a

# View container logs
docker-compose -f docker-compose.yml -f docker-compose.test.yml logs app-test

# Rebuild containers
docker-compose -f docker-compose.yml -f docker-compose.test.yml build --no-cache
```

#### 2. Database Connection Issues
```bash
# Check database health
docker-compose -f docker-compose.yml -f docker-compose.test.yml exec test-db mysqladmin ping

# Reset test database
docker-compose -f docker-compose.yml -f docker-compose.test.yml down -v
docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d
```

#### 3. Tests Failing
```bash
# Run specific test file
docker-compose -f docker-compose.yml -f docker-compose.test.yml exec app-test ./vendor/bin/pest tests/Feature/ExampleTest.php

# Run with verbose output
docker-compose -f docker-compose.yml -f docker-compose.test.yml exec app-test ./vendor/bin/pest --verbose

# Check application logs
docker-compose -f docker-compose.yml -f docker-compose.test.yml logs app-test
```

#### 4. Port Conflicts
```bash
# Check port usage
lsof -i :3307
lsof -i :8080

# Change ports in docker-compose.test.yml if needed
```

### Debug Commands
```bash
# Enter test container shell
docker-compose -f docker-compose.yml -f docker-compose.test.yml exec app-test sh

# Check PHP configuration
docker-compose -f docker-compose.yml -f docker-compose.test.yml exec app-test php --ini

# Test database connection manually
docker-compose -f docker-compose.yml -f docker-compose.test.yml exec app-test php artisan tinker
```

---

## 📈 Performance Optimization

### Test Database Optimization
- **tmpfs**: Database runs in memory for speed
- **Reduced Buffer Pool**: Optimized for testing workload
- **Disabled Features**: Binary logging, doublewrite buffer

### Container Optimization
- **Shared Volumes**: Avoid copying large directories
- **Layer Caching**: Optimized Dockerfile for faster builds
- **Resource Limits**: Prevent resource exhaustion

---

## 🔗 Integration with CI/CD

### GitHub Actions Compatibility
The local testing pipeline mirrors the GitHub Actions workflow:

1. **Same PHP/Node versions**
2. **Same dependency installation**
3. **Same test commands**
4. **Same validation checks**

### Pre-commit Hooks (Optional)
```bash
# Install pre-commit hook
echo '#!/bin/bash\n./scripts/pre-push.sh' > .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

---

## 📚 Best Practices

### Writing Tests
1. **Use descriptive test names**
2. **Test one thing per test**
3. **Use factories for test data**
4. **Clean up after tests**
5. **Test both success and failure scenarios**

### Running Tests
1. **Run tests before committing**
2. **Use appropriate test level for changes**
3. **Monitor test performance**
4. **Keep test data minimal**

### Maintaining Tests
1. **Update tests with code changes**
2. **Remove obsolete tests**
3. **Refactor test code regularly**
4. **Document complex test scenarios**

---

## 🎯 Next Steps

1. **Add Browser Tests**: Implement Laravel Dusk for E2E testing
2. **Performance Testing**: Add load testing with Apache Bench
3. **Security Testing**: Integrate security scanning tools
4. **Test Coverage**: Implement code coverage reporting
5. **Parallel Testing**: Speed up test execution

---

## 📞 Support

If you encounter issues with the testing setup:

1. Check this documentation first
2. Review container logs
3. Verify Docker and Docker Compose versions
4. Check for port conflicts
5. Ensure sufficient system resources

For additional help, refer to:
- [Docker Compose Documentation](DOCKER-COMPOSE.md)
- [Development Guidelines](docs/DEVELOPMENT_CHECKLIST_AND_GUIDELINES.md)
- [Deployment Guide](DEPLOYMENT.md)
